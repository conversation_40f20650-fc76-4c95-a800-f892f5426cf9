"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { BookOpen, Shield, FileCheck, Search, TrendingUp, Database, Palette } from "lucide-react"
import { getBrowserSupabaseClient } from "@/lib/supabase-browser"

// Check if Supabase environment variables are available
const supabase = getBrowserSupabaseClient()

// Default courses for demo mode
const defaultCourses: Course[] = [
  {
    id: "1",
    title: "Vibe Coding Master Class",
    description: "Master modern web development with React, Next.js, and TypeScript",
    price: 25000,
    icon: "code",
    color: "#3B82F6",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20Vibe%20Coding%20Master%20Class",
    featured: true,
  },
  {
    id: "2",
    title: "Ethical Hacking Master Class",
    description: "Learn cybersecurity fundamentals and ethical hacking techniques",
    price: 30000,
    icon: "shield",
    color: "#EF4444",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20Ethical%20Hacking%20Master%20Class",
  },
  {
    id: "3",
    title: "GRC Analyst Master Class",
    description: "Governance, Risk & Compliance analysis for enterprise security",
    price: 35000,
    icon: "check",
    color: "#10B981",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20GRC%20Analyst%20Master%20Class",
  },
  {
    id: "4",
    title: "Digital Marketing Bootcamp",
    description: "Complete digital marketing course covering SEO, SEM, Social Media",
    price: 20000,
    icon: "trending",
    color: "#F59E0B",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20Digital%20Marketing%20Bootcamp",
  },
  {
    id: "5",
    title: "Data Science Fundamentals",
    description: "Learn Python, SQL, Machine Learning, and Data Visualization",
    price: 40000,
    icon: "database",
    color: "#8B5CF6",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20Data%20Science%20Fundamentals",
  },
  {
    id: "6",
    title: "UI/UX Design Mastery",
    description: "Complete course on user interface and user experience design",
    price: 28000,
    icon: "palette",
    color: "#EC4899",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20UI/UX%20Design%20Mastery",
  },
]

interface Course {
  id: string
  title: string
  description: string
  price: number
  icon: string
  color: string
  whatsapp_group_link: string
  featured?: boolean
  active?: boolean
}

const getIcon = (iconName: string, color: string) => {
  const iconProps = { size: 24, color }
  switch (iconName) {
    case "code":
      return <BookOpen {...iconProps} />
    case "shield":
      return <Shield {...iconProps} />
    case "check":
      return <FileCheck {...iconProps} />
    case "trending":
      return <TrendingUp {...iconProps} />
    case "database":
      return <Database {...iconProps} />
    case "palette":
      return <Palette {...iconProps} />
    default:
      return <BookOpen {...iconProps} />
  }
}

export default function CoursesPage() {
  const [courses, setCourses] = useState<Course[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCourses()
  }, [])

  const fetchCourses = async () => {
    if (!supabase) {
      console.log("Supabase not configured, using default courses")
      setCourses(defaultCourses)
      setLoading(false)
      return
    }

    try {
      const { data, error } = await supabase
        .from("courses")
        .select("*")
        .eq("active", true)
        .order("created_at", { ascending: false })

      if (error) {
        // If table doesn't exist or other database error, use default courses
        console.log("Database error, using default courses:", error.message)
        setCourses(defaultCourses)
        setLoading(false)
        return
      }

      setCourses(data && data.length > 0 ? data : defaultCourses)
    } catch (error) {
      console.error("Error fetching courses:", error)
      setCourses(defaultCourses)
    } finally {
      setLoading(false)
    }
  }

  const initializePaystack = () => {
    return new Promise((resolve) => {
      if ((window as any).PaystackPop) {
        resolve((window as any).PaystackPop)
        return
      }

      const script = document.createElement("script")
      script.src = "https://js.paystack.co/v1/inline.js"
      script.onload = () => resolve((window as any).PaystackPop)
      document.head.appendChild(script)
    })
  }

  const handlePayment = async (course: Course) => {
    try {
      const PaystackPop = await initializePaystack()

      const handler = PaystackPop.setup({
        key: "pk_test_ede666e704a4695e24ce26b7a6a032ed212397d7",
        email: "<EMAIL>",
        amount: course.price * 100,
        currency: "NGN",
        ref: `noisypay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        callback: (response: any) => {
          // Store purchase in Supabase
          if (supabase) {
            supabase
              .from("purchases")
              .insert({
                email: response.customer?.email || "<EMAIL>",
                course_id: course.id,
                original_price: course.price,
                discount_amount: 0,
                final_amount: course.price,
                status: "completed",
                paystack_reference: response.reference,
              })
              .then(({ error }) => {
                if (error) console.error("Error storing purchase:", error)
              })
          }

          // Redirect to WhatsApp group
          setTimeout(() => {
            window.open(course.whatsapp_group_link, "_blank")
          }, 2000)
        },
        onClose: () => {
          console.log("Payment cancelled")
        },
      })

      handler.openIframe()
    } catch (error) {
      console.error("Payment initialization error:", error)
    }
  }

  const filteredCourses = courses.filter(
    (course) =>
      course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="min-h-screen bg-[#F8FAFF] pb-20">
      <div className="bg-white px-4 py-6 mb-6">
        <div className="max-w-md mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">All Courses</h1>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <Input
              placeholder="Search courses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      <div className="px-4">
        <div className="max-w-md mx-auto">
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-xl"></div>
                      <div className="flex-1">
                        <div className="h-4 bg-gray-200 rounded mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredCourses.map((course, index) => {
                // Define different solid background colors for cards
                const cardColors = [
                  { bg: "#EBF8FF", border: "#3182CE", hover: "#BEE3F8" }, // Blue
                  { bg: "#F0FFF4", border: "#38A169", hover: "#C6F6D5" }, // Green
                  { bg: "#FFFAF0", border: "#D69E2E", hover: "#FED7AA" }, // Orange
                  { bg: "#FAF5FF", border: "#805AD5", hover: "#E9D8FD" }, // Purple
                  { bg: "#FFF5F5", border: "#E53E3E", hover: "#FED7D7" }, // Red
                  { bg: "#F0FDFA", border: "#319795", hover: "#B2F5EA" }, // Teal
                ]

                const colorScheme = cardColors[index % cardColors.length]

                return (
                  <Card
                    key={course.id}
                    className="border-0 shadow-sm transition-all duration-200 hover:shadow-md cursor-pointer"
                    style={{
                      backgroundColor: colorScheme.bg,
                      borderLeft: `4px solid ${colorScheme.border}`,
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colorScheme.hover
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = colorScheme.bg
                    }}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3 mb-3">
                        <div
                          className="p-3 rounded-xl transition-colors duration-200"
                          style={{ backgroundColor: `${course.color}15` }}
                        >
                          {getIcon(course.icon, course.color)}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 mb-1">{course.title}</h3>
                          <p className="text-sm text-gray-600">{course.description}</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-gray-900">₦{course.price.toLocaleString()}</span>
                        <Button
                          size="sm"
                          className="transition-all duration-200 hover:scale-105"
                          style={{ backgroundColor: course.color }}
                          onClick={() => handlePayment(course)}
                        >
                          Buy Now
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}

              {filteredCourses.length === 0 && !loading && (
                <div className="text-center py-12">
                  <BookOpen size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No courses found</h3>
                  <p className="text-gray-600">
                    {searchTerm ? "Try adjusting your search terms" : "Check back later for new courses"}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
