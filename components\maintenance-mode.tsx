"use client"

import { Card, CardContent } from "@/components/ui/card"
import { AlertTriangle, Clock } from "lucide-react"

export default function MaintenanceMode() {
  return (
    <div className="min-h-screen bg-[#F8FAFF] flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardContent className="p-8">
          <div className="mb-6">
            <div className="mx-auto w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="text-yellow-600" size={32} />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Under Maintenance</h1>
            <p className="text-gray-600">
              Noisypay is currently undergoing scheduled maintenance. We'll be back shortly!
            </p>
          </div>

          <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
            <Clock size={16} />
            <span>Expected downtime: 30 minutes</span>
          </div>

          <div className="mt-6 pt-6 border-t">
            <p className="text-xs text-gray-400">
              For urgent support, contact us at{" "}
              <a href="mailto:<EMAIL>" className="text-blue-600">
                <EMAIL>
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
