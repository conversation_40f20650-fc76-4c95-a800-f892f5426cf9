// Production logging utility
type LogLevel = "info" | "warn" | "error" | "debug"

interface LogEntry {
  level: LogLevel
  message: string
  timestamp: string
  context?: Record<string, any>
  userId?: string
  sessionId?: string
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === "development"

  private log(level: LogLevel, message: string, context?: Record<string, any>) {
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
    }

    // In development, log to console
    if (this.isDevelopment) {
      console[level === "debug" ? "log" : level](
        `[${entry.timestamp}] ${level.toUpperCase()}: ${message}`,
        context || "",
      )
      return
    }

    // In production, you might want to send to a logging service
    // For now, we'll use console but you can integrate with services like:
    // - Vercel Analytics
    // - Sentry
    // - LogRocket
    // - DataDog

    console[level === "debug" ? "log" : level](JSON.stringify(entry))
  }

  info(message: string, context?: Record<string, any>) {
    this.log("info", message, context)
  }

  warn(message: string, context?: Record<string, any>) {
    this.log("warn", message, context)
  }

  error(message: string, context?: Record<string, any>) {
    this.log("error", message, context)
  }

  debug(message: string, context?: Record<string, any>) {
    if (this.isDevelopment) {
      this.log("debug", message, context)
    }
  }
}

export const logger = new Logger()
