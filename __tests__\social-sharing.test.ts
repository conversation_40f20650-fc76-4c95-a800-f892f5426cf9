import { describe, it, expect } from 'vitest'
import { readFileSync } from 'fs'
import { join } from 'path'

describe('Social Sharing Configuration', () => {
  it('should have correct OpenGraph and Twitter metadata in layout.tsx', () => {
    const layoutContent = readFileSync(join(process.cwd(), 'app/layout.tsx'), 'utf-8')
    
    // Check OpenGraph configuration
    expect(layoutContent).toContain('siteName: "NoisyPay"')
    expect(layoutContent).toContain('title: "NoisyPay - Online Training Payments"')
    expect(layoutContent).toContain('url: "/logo.png"')
    expect(layoutContent).toContain('alt: "NoisyPay Logo"')
    
    // Check Twitter card configuration
    expect(layoutContent).toContain('card: "summary_large_image"')
    expect(layoutContent).toContain('images: ["/logo.png"]')
    
    // Check consistent branding
    expect(layoutContent).toContain('default: "NoisyPay - Online Training Payments"')
    expect(layoutContent).toContain('template: "%s | NoisyPay"')
    expect(layoutContent).toContain('creator: "NoisyPay"')
    expect(layoutContent).toContain('publisher: "NoisyPay"')
  })

  it('should reference the correct logo asset for social sharing', () => {
    const layoutContent = readFileSync(join(process.cwd(), 'app/layout.tsx'), 'utf-8')
    
    // Verify both OpenGraph and Twitter use the same logo asset
    const ogImageMatches = layoutContent.match(/openGraph:[\s\S]*?url: "([^"]+)"/);
    const twitterImageMatches = layoutContent.match(/twitter:[\s\S]*?images: \["([^"]+)"\]/);
    
    expect(ogImageMatches?.[1]).toBe('/logo.png')
    expect(twitterImageMatches?.[1]).toBe('/logo.png')
  })
})