import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { config } from "@/lib/config"

export async function GET() {
  try {
    const checks = {
      timestamp: new Date().toISOString(),
      status: "healthy",
      version: config.app.version,
      environment: process.env.NODE_ENV,
      checks: {
        database: "unknown",
        paystack: "unknown",
      },
    }

    // Check database connection
    if (config.database.url && config.database.anonKey) {
      try {
        const supabase = createClient(config.database.url, config.database.anonKey)
        const { error } = await supabase.from("courses").select("count").limit(1)
        checks.checks.database = error ? "unhealthy" : "healthy"
      } catch {
        checks.checks.database = "unhealthy"
      }
    }

    // Check Paystack configuration
    checks.checks.paystack = config.payment.paystack.publicKey ? "configured" : "not_configured"

    const isHealthy = Object.values(checks.checks).every((status) => status === "healthy" || status === "configured")

    return NextResponse.json(checks, {
      status: isHealthy ? 200 : 503,
    })
  } catch (error) {
    return NextResponse.json(
      {
        status: "unhealthy",
        error: "Health check failed",
        timestamp: new Date().toISOString(),
      },
      { status: 503 },
    )
  }
}
