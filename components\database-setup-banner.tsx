"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { AlertTriangle, X, Database } from "lucide-react"

export default function DatabaseSetupBanner() {
  const [isVisible, setIsVisible] = useState(true)

  // Check if we're in demo mode (no Supabase configured)
  const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!isDemoMode || !isVisible) {
    return null
  }

  return (
    <Card className="mx-4 mt-4 border-yellow-200 bg-yellow-50">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className="text-yellow-600 mt-0.5" size={20} />
          <div className="flex-1">
            <h3 className="font-semibold text-yellow-900 mb-1">Database Setup Required</h3>
            <p className="text-sm text-yellow-800 mb-3">
              To use all features, please set up your Supabase database by running the SQL scripts in the scripts
              folder.
            </p>
            <div className="flex gap-2">
              <Button
                size="sm"
                className="bg-yellow-600 hover:bg-yellow-700 text-white"
                onClick={() => window.open("https://supabase.com/dashboard", "_blank")}
              >
                <Database size={14} className="mr-1" />
                Open Supabase
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="border-yellow-300 text-yellow-800 hover:bg-yellow-100 bg-transparent"
                onClick={() => setIsVisible(false)}
              >
                Dismiss
              </Button>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
            className="text-yellow-600 hover:text-yellow-700 hover:bg-yellow-100"
          >
            <X size={16} />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
