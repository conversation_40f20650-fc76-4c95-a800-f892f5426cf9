import { type NextRequest, NextResponse } from "next/server"
import { createAdminSession, clearAdminSession } from "@/lib/auth"
import { logger } from "@/lib/logger"
import { createServiceClient } from "@/lib/supabase"
import bcrypt from "bcryptjs"

export async function POST(request: NextRequest) {
  try {
    const { password, action } = await request.json()

    if (action === "login") {
      if (!password) {
        return NextResponse.json({ error: "Password is required" }, { status: 400 })
      }

      // Validate against database-stored hash
      const supabase = createServiceClient()
      const { data, error } = await supabase
        .from("admin_users")
        .select("password_hash")
        .eq("active", true)
        .limit(1)
        .single()

      if (error || !data?.password_hash) {
        logger.warn("Admin login failed - no admin user configured")
        return NextResponse.json({ error: "Admin not configured" }, { status: 401 })
      }

      const isValid = await bcrypt.compare(password, data.password_hash)
      if (!isValid) {
        logger.warn("Admin login failed - invalid password")
        return NextResponse.json({ error: "Invalid password" }, { status: 401 })
      }

      createAdminSession()
      logger.info("Admin login successful")

      return NextResponse.json({
        success: true,
        message: "Login successful",
      })
    }

    if (action === "logout") {
      clearAdminSession()
      logger.info("Admin logout successful")

      return NextResponse.json({
        success: true,
        message: "Logout successful",
      })
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 })
  } catch (error) {
    logger.error("Admin auth error", { error: error.message })
    return NextResponse.json({ error: "Authentication failed" }, { status: 500 })
  }
}
