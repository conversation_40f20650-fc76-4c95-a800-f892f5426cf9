import { NextResponse } from "next/server"
import { verifyAdminSession } from "@/lib/auth"
import { createServiceClient } from "@/lib/supabase"

export async function GET() {
  try {
    const isAuthenticated = verifyAdminSession()
    const supabase = createServiceClient()
    const { data, error } = await supabase
      .from("admin_users")
      .select("id")
      .eq("active", true)
      .limit(1)
      .single()

    const hasAdmin = !error && !!data

    return NextResponse.json({
      isAuthenticated,
      hasAdmin,
      // kept for backward-compatibility with existing client components
      isDefaultPassword: false,
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to get admin status" }, { status: 500 })
  }
}
