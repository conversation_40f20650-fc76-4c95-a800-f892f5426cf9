"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Download, X } from "lucide-react"

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: "accepted" | "dismissed" }>
}

export default function PWAInstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showPrompt, setShowPrompt] = useState(false)

  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)

      // Check if user has dismissed the prompt before
      try {
        const hasSeenPrompt = localStorage.getItem("pwa-install-dismissed")
        if (!hasSeenPrompt) {
          setShowPrompt(true)
        }
      } catch (error) {
        console.log("localStorage not available")
      }
    }

    // Only add event listener in browser environment
    if (typeof window !== "undefined") {
      window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt)
    }

    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt)
      }
    }
  }, [])

  const handleInstall = async () => {
    if (!deferredPrompt) return

    try {
      await deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice

      if (outcome === "accepted") {
        console.log("User accepted the install prompt")
      }

      setDeferredPrompt(null)
      setShowPrompt(false)
    } catch (error) {
      console.error("Error during installation:", error)
      setShowPrompt(false)
    }
  }

  const handleDismiss = () => {
    setShowPrompt(false)
    try {
      localStorage.setItem("pwa-install-dismissed", "true")
    } catch (error) {
      console.log("localStorage not available")
    }
  }

  if (!showPrompt || !deferredPrompt) {
    return null
  }

  return (
    <Card className="mx-4 mt-4 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Download className="text-blue-600" size={20} />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-blue-900 mb-1">Install Noisypay</h3>
            <p className="text-sm text-blue-800 mb-3">
              Get the full app experience! Install Noisypay for faster access and offline features.
            </p>
            <div className="flex gap-2">
              <Button size="sm" onClick={handleInstall} className="bg-blue-600 hover:bg-blue-700">
                Install App
              </Button>
              <Button size="sm" variant="outline" onClick={handleDismiss}>
                Maybe Later
              </Button>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-100"
          >
            <X size={16} />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
