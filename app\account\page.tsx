"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { User, Settings, HelpCircle, Shield, LogOut } from "lucide-react"

export default function AccountPage() {
  const menuItems = [
    { icon: User, label: "Profile Settings", href: "#" },
    { icon: Settings, label: "App Settings", href: "#" },
    { icon: HelpCircle, label: "Help & Support", href: "/support" },
    { icon: Shield, label: "Privacy Policy", href: "#" },
  ]

  return (
    <div className="min-h-screen bg-[#F8FAFF] pb-20">
      <div className="bg-white px-4 py-6 mb-6">
        <div className="max-w-md mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Admin Account</h1>
          <p className="text-gray-600">Manage your Noisypay admin account settings</p>
        </div>
      </div>

      <div className="px-4">
        <div className="max-w-md mx-auto">
          {/* Profile Card */}
          <Card className="mb-6 border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">AD</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Noisypay Admin</h3>
                  <p className="text-sm text-gray-600"><EMAIL></p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Menu Items */}
          <div className="space-y-2">
            {menuItems.map((item, index) => {
              const Icon = item.icon
              return (
                <Card key={index} className="border-0 shadow-sm hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <button
                      className="flex items-center gap-3 w-full text-left"
                      onClick={() => item.href !== "#" && (window.location.href = item.href)}
                    >
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <Icon size={20} className="text-gray-600" />
                      </div>
                      <span className="font-medium text-gray-900">{item.label}</span>
                    </button>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Admin Actions */}
          <Card className="mt-6 border-0 shadow-sm">
            <CardContent className="p-4">
              <Button
                variant="outline"
                className="w-full bg-transparent mb-3"
                onClick={() => (window.location.href = "/admin")}
              >
                <Shield size={16} className="mr-2" />
                Admin Dashboard
              </Button>
              <Button
                variant="outline"
                className="w-full bg-transparent text-red-600 border-red-200 hover:bg-red-50"
                onClick={() => (window.location.href = "/")}
              >
                <LogOut size={16} className="mr-2" />
                Logout
              </Button>
            </CardContent>
          </Card>

          {/* App Info */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>Noisypay Admin Panel v1.0.0</p>
            <p>© 2024 Noisypay. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
