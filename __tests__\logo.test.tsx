import React from "react"
import { render, screen } from "@testing-library/react"
import Logo, { LogoIcon } from "@/components/logo"

describe("Logo", () => {
  it("renders icon and brand text", () => {
    render(<Logo size={24} />)
    expect(screen.getByAltText(/NoisyPay Logo/i)).toBeInTheDocument()
    expect(screen.getByText(/NoisyPay/i)).toBeInTheDocument()
  })

  it("renders icon-only variant", () => {
    render(<LogoIcon size={24} />)
    expect(screen.getByAltText(/NoisyPay/i)).toBeInTheDocument()
  })
})



