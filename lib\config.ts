// Production configuration
export const config = {
  // App Info
  app: {
    name: "Noisypay",
    description: "Tech Skills Payment Platform",
    url: process.env.NEXT_PUBLIC_APP_URL || "https://noisypay.vercel.app",
    version: "1.0.0",
  },

  // Database
  database: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
  },

  // Payment
  payment: {
    paystack: {
      publicKey: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,
      secretKey: process.env.PAYSTACK_SECRET_KEY,
    },
  },

  // WhatsApp
  whatsapp: {
    number: "2347018643642",
  },

  // Features
  features: {
    analytics: process.env.NODE_ENV === "production",
    errorReporting: process.env.NODE_ENV === "production",
    maintenance: process.env.MAINTENANCE_MODE === "true",
  },
}

// Validation (server-side only)
export const validateConfig = () => {
  const errors: string[] = []

  if (!config.database.url) {
    errors.push("NEXT_PUBLIC_SUPABASE_URL is required")
  }

  if (!config.database.anonKey) {
    errors.push("NEXT_PUBLIC_SUPABASE_ANON_KEY is required")
  }

  if (!config.payment.paystack.publicKey) {
    errors.push("NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY is required")
  }

  return errors
}
