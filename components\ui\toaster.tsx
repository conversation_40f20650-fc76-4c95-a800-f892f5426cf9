"use client"

import { Toast, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport } from "@/components/ui/toast"
import { useToast } from "@/hooks/use-toast"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(({ id, title, description, action, variant, ...props }) => (
        <Toast
          key={id}
          {...props}
          className={
            variant === "destructive"
              ? "bg-red-600 text-white border-red-700"
              : "bg-slate-800 text-white border-slate-700"
          }
        >
          <div className="grid gap-1">
            {title && <ToastTitle className="text-white">{title}</ToastTitle>}
            {description && <ToastDescription className="text-gray-100">{description}</ToastDescription>}
          </div>
          {action}
          <ToastClose className="text-white hover:text-gray-200" />
        </Toast>
      ))}
      <ToastViewport />
    </ToastProvider>
  )
}
