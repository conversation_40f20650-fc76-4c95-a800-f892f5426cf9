import { NextResponse } from "next/server"
import { createServiceClient } from "@/lib/supabase"
import { verifyAdminSession } from "@/lib/auth"

export async function GET() {
  try {
    if (!verifyAdminSession()) return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    const supabase = createServiceClient()
    const { data, error } = await supabase.from("courses").select("*").order("created_at", { ascending: false })
    if (error) return NextResponse.json({ error: error.message }, { status: 500 })
    return NextResponse.json({ courses: data || [] })
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    if (!verifyAdminSession()) return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    const body = await request.json()
    const supabase = createServiceClient()
    const { error } = await supabase.from("courses").insert(body)
    if (error) return NextResponse.json({ error: error.message }, { status: 500 })
    return NextResponse.json({ success: true })
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}


