"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Trash2, Edit, Plus, Eye, EyeOff, User, LogOut, Home, BookOpen, CreditCard, MessageCircle } from "lucide-react"
import { LogoIcon } from "@/components/logo"
import { getBrowserSupabaseClient } from "@/lib/supabase-browser"

// Check if Supabase environment variables are available
const supabase = getBrowserSupabaseClient()

interface Course {
  id: string
  title: string
  description: string
  price: number
  icon: string
  color: string
  whatsapp_group_link: string
  featured?: boolean
  active?: boolean
}

interface AdminStatus {
  isAuthenticated: boolean
  hasAdmin?: boolean
}

const iconOptions = [
  { value: "code", label: "Code" },
  { value: "shield", label: "Shield" },
  { value: "check", label: "Check" },
  { value: "trending", label: "Trending" },
  { value: "database", label: "Database" },
  { value: "palette", label: "Palette" },
]

const colorOptions = [
  { value: "#3B82F6", label: "Blue" },
  { value: "#EF4444", label: "Red" },
  { value: "#10B981", label: "Green" },
  { value: "#F59E0B", label: "Yellow" },
  { value: "#8B5CF6", label: "Purple" },
  { value: "#EC4899", label: "Pink" },
]

export default function AdminPage() {
  const [adminStatus, setAdminStatus] = useState<AdminStatus>({
    isAuthenticated: false,
    hasAdmin: false,
  })

  // Preload logo-icon.png for this page specifically
  useEffect(() => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = '/logo-icon.png'
    link.as = 'image'
    document.head.appendChild(link)
    
    return () => {
      document.head.removeChild(link)
    }
  }, [])
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [courses, setCourses] = useState<Course[]>([])
  const [editingCourse, setEditingCourse] = useState<Course | null>(null)
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    price: "",
    icon: "code",
    color: "#3B82F6",
    whatsapp_group_link: "",
    featured: false,
    active: true,
  })
  const [loading, setLoading] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    checkAdminStatus()
  }, [])

  useEffect(() => {
    if (adminStatus.isAuthenticated) {
      fetchCourses()
    }
  }, [adminStatus.isAuthenticated])

  const checkAdminStatus = async () => {
    try {
      const response = await fetch("/api/admin/status")
      if (response.ok) {
        const status = await response.json()
        setAdminStatus(status)
      }
    } catch (error) {
      console.error("Failed to check admin status:", error)
    }
  }

  const handleLogin = async () => {
    if (!password.trim()) {
      toast({
        title: "Error",
        description: "Please enter a password",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch("/api/admin/auth", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "login",
          password: password,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setAdminStatus((prev) => ({ ...prev, isAuthenticated: true }))
        toast({
          title: "Access Granted",
          description: "Welcome to the Noisypay admin panel",
        })
        setPassword("")
      } else {
        toast({
          title: "Access Denied",
          description: data.error || "Invalid password",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to authenticate. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      await fetch("/api/admin/auth", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "logout",
        }),
      })

      setAdminStatus((prev) => ({ ...prev, isAuthenticated: false }))
      toast({
        title: "Logged Out",
        description: "You have been logged out successfully",
      })
    } catch (error) {
      console.error("Logout error:", error)
    }
  }

  const fetchCourses = async () => {
    try {
      const res = await fetch("/api/admin/courses")
      if (!res.ok) throw new Error("Failed to fetch courses")
      const json = await res.json()
      setCourses(json.courses || [])
    } catch (error) {
      console.error("Error fetching courses:", error)
      setCourses([])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    setLoading(true)

    try {
      const courseData = {
        title: formData.title,
        description: formData.description,
        price: Number.parseInt(formData.price),
        icon: formData.icon,
        color: formData.color,
        whatsapp_group_link: formData.whatsapp_group_link,
        featured: formData.featured,
        active: formData.active,
      }

      if (editingCourse) {
        const res = await fetch(`/api/admin/courses/${editingCourse.id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(courseData),
        })
        if (!res.ok) throw new Error("Failed to update course")
        toast({ title: "Success", description: "Course updated successfully" })
      } else {
        const res = await fetch(`/api/admin/courses`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(courseData),
        })
        if (!res.ok) throw new Error("Failed to create course")
        toast({ title: "Success", description: "Course created successfully" })
      }

      // Reset form and editing state
      setFormData({
        title: "",
        description: "",
        price: "",
        icon: "code",
        color: "#3B82F6",
        whatsapp_group_link: "",
        featured: false,
        active: true,
      })
      setEditingCourse(null)

      // Refresh courses list
      await fetchCourses()
    } catch (error: any) {
      console.error("Error saving course:", error)
      toast({
        title: "Error",
        description: `Failed to save course: ${error.message}`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (course: Course) => {
    setEditingCourse(course)
    setFormData({
      title: course.title,
      description: course.description,
      price: course.price.toString(),
      icon: course.icon,
      color: course.color,
      whatsapp_group_link: course.whatsapp_group_link,
      featured: course.featured || false,
      active: course.active !== false,
    })
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this course? This action cannot be undone.")) return

    setDeleteLoading(id)
    try {
      const res = await fetch(`/api/admin/courses/${id}`, { method: "DELETE" })
      if (!res.ok) throw new Error("Failed to delete course")

      toast({
        title: "Success",
        description: "Course deleted successfully",
      })

      // Refresh the courses list
      await fetchCourses()
    } catch (error: any) {
      console.error("Error deleting course:", error)
      toast({
        title: "Error",
        description: `Failed to delete course: ${error.message}`,
        variant: "destructive",
      })
    } finally {
      setDeleteLoading(null)
    }
  }

  const cancelEdit = () => {
    setEditingCourse(null)
    setFormData({
      title: "",
      description: "",
      price: "",
      icon: "code",
      color: "#3B82F6",
      whatsapp_group_link: "",
      featured: false,
      active: true,
    })
  }

  if (!adminStatus.isAuthenticated) {
    return (
      <div className="min-h-screen bg-[#F8FAFF] flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User size={20} />
              Noisypay Admin Access
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Security warning removed per request */}

              <div>
                <Label htmlFor="password">Admin Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && !loading && handleLogin()}
                    placeholder="Enter admin password"
                    disabled={loading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </Button>
                </div>
              </div>
              <Button onClick={handleLogin} className="w-full" disabled={loading}>
                {loading ? "Authenticating..." : "Login to Admin Panel"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#F8FAFF] p-4 pb-20">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <LogoIcon size={50} />
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => (window.location.href = "/account")}>
              <User size={16} className="mr-2" />
              Account
            </Button>
            <Button variant="outline" onClick={handleLogout}>
              <LogOut size={16} className="mr-2" />
              Logout
            </Button>
          </div>
        </div>

        {/* Security warning removed per request */}

        {/* Add/Edit Course Form */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>{editingCourse ? "Edit Course" : "Add New Course"}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Course Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="e.g., Web Development Bootcamp"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="price">Price (₦)</Label>
                  <Input
                    id="price"
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                    placeholder="25000"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Course Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Describe what students will learn in this course..."
                  required
                />
              </div>

              <div>
                <Label htmlFor="whatsapp_link">WhatsApp Group Link</Label>
                <Input
                  id="whatsapp_link"
                  value={formData.whatsapp_group_link}
                  onChange={(e) => setFormData({ ...formData, whatsapp_group_link: e.target.value })}
                  placeholder="https://wa.me/2347018643642?text=I%20just%20paid%20for%20Course%20Name"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="icon">Course Icon</Label>
                  <Select value={formData.icon} onValueChange={(value) => setFormData({ ...formData, icon: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {iconOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="color">Course Color</Label>
                  <Select value={formData.color} onValueChange={(value) => setFormData({ ...formData, color: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded" style={{ backgroundColor: option.value }} />
                            {option.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="featured"
                    checked={formData.featured}
                    onChange={(e) => setFormData({ ...formData, featured: e.target.checked })}
                  />
                  <Label htmlFor="featured">Featured Course</Label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="active"
                    checked={formData.active}
                    onChange={(e) => setFormData({ ...formData, active: e.target.checked })}
                  />
                  <Label htmlFor="active">Active Course</Label>
                </div>
              </div>

              <div className="flex gap-2">
                <Button type="submit" disabled={loading}>
                  <Plus size={16} className="mr-2" />
                  {loading ? "Saving..." : editingCourse ? "Update Course" : "Add Course"}
                </Button>
                {editingCourse && (
                  <Button type="button" variant="outline" onClick={cancelEdit}>
                    Cancel
                  </Button>
                )}
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Existing Courses */}
        <Card>
          <CardHeader>
            <CardTitle>Existing Courses ({courses.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {courses.map((course) => (
                <div key={course.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-10 h-10 rounded-lg flex items-center justify-center"
                      style={{ backgroundColor: `${course.color}15` }}
                    >
                      <div className="w-5 h-5 rounded" style={{ backgroundColor: course.color }} />
                    </div>
                    <div>
                      <h3 className="font-semibold">{course.title}</h3>
                      <p className="text-sm text-gray-600">₦{course.price.toLocaleString()}</p>
                      <div className="flex gap-2 mt-1">
                        {course.featured && (
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">Featured</span>
                        )}
                        {course.active === false && (
                          <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">Inactive</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(course)}
                      disabled={deleteLoading === course.id}
                    >
                      <Edit size={16} />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(course.id)}
                      disabled={deleteLoading === course.id}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      {deleteLoading === course.id ? (
                        <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <Trash2 size={16} />
                      )}
                    </Button>
                  </div>
                </div>
              ))}
              {courses.length === 0 && (
                <p className="text-center text-gray-500 py-8">No courses found. Add your first course above.</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      {/* Bottom nav for mobile */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-50 md:hidden">
        <div className="max-w-md mx-auto">
          <div className="flex items-center justify-around">
            <button className="flex flex-col items-center gap-1 py-2 px-3 text-gray-600" onClick={() => (window.location.href = "/") }>
              <Home size={20} />
              <span className="text-xs font-medium">Home</span>
            </button>
            <button className="flex flex-col items-center gap-1 py-2 px-3 text-gray-600" onClick={() => (window.location.href = "/courses") }>
              <BookOpen size={20} />
              <span className="text-xs font-medium">Courses</span>
            </button>
            <button className="flex flex-col items-center gap-1 py-2 px-3 text-gray-600" onClick={() => (window.location.href = "/payments") }>
              <CreditCard size={20} />
              <span className="text-xs font-medium">Payments</span>
            </button>
            <button className="flex flex-col items-center gap-1 py-2 px-3 text-gray-600" onClick={() => (window.location.href = "/support") }>
              <MessageCircle size={20} />
              <span className="text-xs font-medium">Support</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
