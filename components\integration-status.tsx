"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, AlertCircle, Database, CreditCard, MessageCircle, Shield } from "lucide-react"
import { getBrowserSupabaseClient } from "@/lib/supabase-browser"

const supabase = getBrowserSupabaseClient()

interface IntegrationCheck {
  name: string
  status: "success" | "warning" | "error"
  message: string
  icon: any
}

interface AdminStatus {
  isDefaultPassword: boolean
}

export default function IntegrationStatus() {
  const [checks, setChecks] = useState<IntegrationCheck[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    runIntegrationChecks()
  }, [])

  const runIntegrationChecks = async () => {
    const integrationChecks: IntegrationCheck[] = []

    // Check Supabase Connection
    if (!supabase) {
      integrationChecks.push({
        name: "Supabase Database",
        status: "error",
        message: "Environment variables not configured",
        icon: Database,
      })
    } else {
      try {
        const { data, error } = await supabase.from("courses").select("count").limit(1)
        if (error) {
          integrationChecks.push({
            name: "Supabase Database",
            status: "error",
            message: `Database error: ${error.message}`,
            icon: Database,
          })
        } else {
          integrationChecks.push({
            name: "Supabase Database",
            status: "success",
            message: "Connected and tables exist",
            icon: Database,
          })
        }
      } catch (error) {
        integrationChecks.push({
          name: "Supabase Database",
          status: "error",
          message: "Connection failed",
          icon: Database,
        })
      }
    }

    // Check Tables
    if (supabase) {
      const tables = ["courses", "purchases", "coupons", "admin_users"]
      for (const table of tables) {
        try {
          const { error } = await supabase.from(table).select("count").limit(1)
          if (error) {
            integrationChecks.push({
              name: `${table.charAt(0).toUpperCase() + table.slice(1)} Table`,
              status: "error",
              message: `Table missing or inaccessible`,
              icon: Database,
            })
          } else {
            integrationChecks.push({
              name: `${table.charAt(0).toUpperCase() + table.slice(1)} Table`,
              status: "success",
              message: "Table exists and accessible",
              icon: Database,
            })
          }
        } catch (error) {
          integrationChecks.push({
            name: `${table.charAt(0).toUpperCase() + table.slice(1)} Table`,
            status: "error",
            message: "Check failed",
            icon: Database,
          })
        }
      }
    }

    // Check Paystack Integration
    const paystackKey = process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY
    if (paystackKey && paystackKey.startsWith("pk_")) {
      integrationChecks.push({
        name: "Paystack Payment",
        status: "success",
        message: "Payment key configured correctly",
        icon: CreditCard,
      })
    } else {
      integrationChecks.push({
        name: "Paystack Payment",
        status: "error",
        message: "Invalid or missing Paystack key",
        icon: CreditCard,
      })
    }

    // Check WhatsApp Integration
    const whatsappNumber = "2347018643642"
    if (whatsappNumber) {
      integrationChecks.push({
        name: "WhatsApp Integration",
        status: "success",
        message: `Configured for +${whatsappNumber}`,
        icon: MessageCircle,
      })
    } else {
      integrationChecks.push({
        name: "WhatsApp Integration",
        status: "warning",
        message: "WhatsApp number not configured",
        icon: MessageCircle,
      })
    }

    // Check Admin Security (server-side)
    try {
      const response = await fetch("/api/admin/status")
      if (response.ok) {
        const adminStatus: AdminStatus = await response.json()
        if (adminStatus.isDefaultPassword) {
          integrationChecks.push({
            name: "Admin Security",
            status: "warning",
            message: "Using default password - change for production",
            icon: Shield,
          })
        } else {
          integrationChecks.push({
            name: "Admin Security",
            status: "success",
            message: "Custom admin password configured",
            icon: Shield,
          })
        }
      } else {
        integrationChecks.push({
          name: "Admin Security",
          status: "error",
          message: "Unable to check admin configuration",
          icon: Shield,
        })
      }
    } catch (error) {
      integrationChecks.push({
        name: "Admin Security",
        status: "error",
        message: "Admin status check failed",
        icon: Shield,
      })
    }

    setChecks(integrationChecks)
    setLoading(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-700 border-green-200"
      case "warning":
        return "bg-yellow-100 text-yellow-700 border-yellow-200"
      case "error":
        return "bg-red-100 text-red-700 border-red-200"
      default:
        return "bg-gray-100 text-gray-700 border-gray-200"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle size={16} className="text-green-600" />
      case "warning":
        return <AlertCircle size={16} className="text-yellow-600" />
      case "error":
        return <XCircle size={16} className="text-red-600" />
      default:
        return <AlertCircle size={16} className="text-gray-600" />
    }
  }

  const successCount = checks.filter((c) => c.status === "success").length
  const totalChecks = checks.length

  return (
    <div className="min-h-screen bg-[#F8FAFF] p-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Integration Status</h1>
          <p className="text-gray-600">System health check and integration status for Noisypay</p>
          {!loading && (
            <div className="mt-4">
              <Badge
                className={`text-lg px-4 py-2 ${successCount === totalChecks ? "bg-green-100 text-green-700" : "bg-yellow-100 text-yellow-700"}`}
              >
                {successCount}/{totalChecks} Integrations Working
              </Badge>
            </div>
          )}
        </div>

        <div className="grid gap-4">
          {loading ? (
            <Card>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            checks.map((check, index) => {
              const Icon = check.icon
              return (
                <Card key={index} className="border-0 shadow-sm">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <Icon size={20} className="text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{check.name}</h3>
                        <p className="text-sm text-gray-600">{check.message}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(check.status)}
                        <Badge className={getStatusColor(check.status)}>
                          {check.status.charAt(0).toUpperCase() + check.status.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })
          )}
        </div>

        {!loading && (
          <Card className="mt-6 border-2 border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-900">Next Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-blue-800">
                {checks.some((c) => c.status === "error") && (
                  <p>• Fix any error status items above before going live</p>
                )}
                {checks.some((c) => c.status === "warning") && (
                  <p>• Address warning items for better security and functionality</p>
                )}
                <p>• Test the complete payment flow with Paystack test cards</p>
                <p>• Verify WhatsApp group links are working correctly</p>
                <p>• Set up production Paystack keys when ready to go live</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
