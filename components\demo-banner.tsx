"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AlertCircle, X } from "lucide-react"

export default function DemoBanner() {
  const [isVisible, setIsVisible] = useState(true)

  // Check if we're in demo mode (no Supabase configured)
  const isDemoMode = !process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!isDemoMode || !isVisible) {
    return null
  }

  return (
    <Card className="mx-4 mt-4 border-orange-200 bg-orange-50">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <AlertCircle className="text-orange-600 mt-0.5" size={20} />
          <div className="flex-1">
            <h3 className="font-semibold text-orange-900 mb-1">Demo Mode</h3>
            <p className="text-sm text-orange-800">
              Noisypay is running in demo mode. Configure Supabase and Paystack for full functionality.
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
            className="text-orange-600 hover:text-orange-700 hover:bg-orange-100"
          >
            <X size={16} />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
