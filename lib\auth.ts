// Server-side authentication utilities
import { cookies } from "next/headers"
import type { NextRequest } from "next/server"

const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || "%434ykdfDET_-#"
const SESSION_COOKIE = "noisypay_admin_session"

// Generate a simple session token
function generateSessionToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

// Verify admin password (server-side only)
export function verifyAdminPassword(password: string): boolean {
  return password === ADMIN_PASSWORD
}

// Create admin session
export function createAdminSession(): string {
  const token = generateSessionToken()
  const cookieStore = cookies()

  cookieStore.set(SESSION_COOKIE, token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
    maxAge: 60 * 60 * 24, // 24 hours
  })

  return token
}

// Verify admin session
export function verifyAdminSession(): boolean {
  try {
    const cookieStore = cookies()
    const session = cookieStore.get(SESSION_COOKIE)
    return !!session?.value
  } catch {
    return false
  }
}

// Clear admin session
export function clearAdminSession(): void {
  const cookieStore = cookies()
  cookieStore.delete(SESSION_COOKIE)
}

// Middleware helper to check admin session
export function isAdminAuthenticated(request: NextRequest): boolean {
  const session = request.cookies.get(SESSION_COOKIE)
  return !!session?.value
}

// Get admin status for client components
export function getAdminStatus(): { isDefaultPassword: boolean } {
  return {
    isDefaultPassword: ADMIN_PASSWORD === "%434ykdfDET_-#",
  }
}
