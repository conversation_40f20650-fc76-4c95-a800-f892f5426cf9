{"buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "rewrites": [{"source": "/health", "destination": "/api/health"}]}