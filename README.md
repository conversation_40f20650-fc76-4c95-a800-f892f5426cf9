# 🎓 Noisypay - Online Training Payments

![Noisypay Logo](./public/logo.png)

A modern, mobile-first web application for online training course payments with instant WhatsApp group access.

## ✨ Features

- 📱 **Mobile-First PWA** - Installable progressive web app
- 💳 **Secure Payments** - Paystack integration for Nigerian payments
- 🎯 **Instant Access** - Automatic WhatsApp group redirect after payment
- 🎫 **Coupon System** - Percentage and fixed discount coupons
- 👨‍💼 **Admin Panel** - Course management and analytics
- 🗄️ **Supabase Backend** - Real-time database with authentication
- 🎨 **Modern UI** - Clean, responsive design with Tailwind CSS

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account
- Paystack account

### Installation

1. **Clone the repository**
\`\`\`bash
git clone https://github.com/yourusername/noisypay.git
cd noisypay
\`\`\`

2. **Install dependencies**
\`\`\`bash
npm install
\`\`\`

3. **Set up environment variables**
\`\`\`bash
cp .env.example .env.local
\`\`\`

Fill in your environment variables:
\`\`\`env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=your_paystack_public_key
PAYSTACK_SECRET_KEY=your_paystack_secret_key
ADMIN_PASSWORD=your_secure_admin_password
NEXT_PUBLIC_APP_URL=http://localhost:3000
\`\`\`

4. **Set up the database**
- Go to your Supabase dashboard
- Run the SQL scripts in the `scripts/` folder:
  - `scripts/create-tables.sql`
  - `scripts/seed-data.sql`

5. **Start the development server**
\`\`\`bash
npm run dev
\`\`\`

Visit `http://localhost:3000` to see your app!

## 📁 Project Structure

\`\`\`
noisypay/
├── app/                    # Next.js 14 App Router
│   ├── admin/             # Admin panel
│   ├── api/               # API routes
│   ├── courses/           # Course pages
│   └── ...
├── components/            # Reusable UI components
├── lib/                   # Utilities and configurations
├── public/               # Static assets
├── scripts/              # Database scripts
└── ...
\`\`\`

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS + shadcn/ui
- **Database**: Supabase (PostgreSQL)
- **Payments**: Paystack
- **Deployment**: Vercel
- **PWA**: Next.js PWA features

## 🔧 Configuration

### Admin Panel
Access the admin panel at `/admin` with your configured password.

### Payment Integration
The app uses Paystack for payments. Configure your keys in the environment variables.

### WhatsApp Integration
Each course has a WhatsApp group link that users are redirected to after successful payment.

## 📱 PWA Features

- Installable on mobile devices
- Offline capability
- Push notifications ready
- App-like experience

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy!

### Manual Deployment
\`\`\`bash
npm run build
npm start
\`\`\`

## 🔒 Security Features

- Secure admin authentication with server-side sessions
- Rate limiting on API routes
- CSRF protection
- Secure headers
- Input validation
- SQL injection protection via Supabase RLS

## 📊 Monitoring

- Health check endpoint: `/api/health`
- Integration status: `/status`
- Error logging and monitoring ready

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

- **Email**: <EMAIL>
- **WhatsApp**: +234 ************
- **Documentation**: Check the `/docs` folder

## 🎯 Roadmap

- [ ] Multi-language support
- [ ] Advanced analytics dashboard
- [ ] Email notifications
- [ ] Course progress tracking
- [ ] Mobile app (React Native)

---

Made with ❤️ by the Noisypay Team
