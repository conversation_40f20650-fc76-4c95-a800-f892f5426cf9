"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MessageCircle, Phone, Mail, HelpCircle } from "lucide-react"

export default function SupportPage() {
  const supportOptions = [
    {
      icon: MessageCircle,
      title: "WhatsApp Support",
      description: "Get instant help via WhatsApp",
      action: "Chat Now",
      href: "https://wa.me/2347018643642?text=Hi,%20I%20need%20help%20with%20Noisypay",
      color: "#25D366",
    },
    {
      icon: Phone,
      title: "Call Support",
      description: "Speak directly with our support team",
      action: "Call Now",
      href: "tel:+2347018643642",
      color: "#3B82F6",
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Send us an email for detailed inquiries",
      action: "Send Email",
      href: "mailto:<EMAIL>",
      color: "#EF4444",
    },
  ]

  const faqs = [
    {
      question: "How do I join a course after payment?",
      answer: "After successful payment, you'll be automatically redirected to the WhatsApp group for your course.",
    },
    {
      question: "Can I get a refund?",
      answer: "Refunds are available within 7 days of purchase if you haven't accessed the course materials.",
    },
    {
      question: "How do I apply a coupon code?",
      answer:
        "Click 'Apply Coupon' on any course card, enter your code, and the discount will be applied automatically.",
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major cards, bank transfers, and mobile money through Paystack.",
    },
  ]

  return (
    <div className="min-h-screen bg-[#F8FAFF] pb-20">
      <div className="bg-white px-4 py-6 mb-6">
        <div className="max-w-md mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Support Center</h1>
          <p className="text-gray-600">We're here to help you with any questions or issues</p>
        </div>
      </div>

      <div className="px-4">
        <div className="max-w-md mx-auto">
          {/* Contact Options */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Get in Touch</h2>
            <div className="space-y-3">
              {supportOptions.map((option, index) => {
                const Icon = option.icon
                return (
                  <Card key={index} className="border-0 shadow-sm hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div
                          className="p-3 rounded-lg"
                          style={{ backgroundColor: `${option.color}15`, color: option.color }}
                        >
                          <Icon size={24} />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900">{option.title}</h3>
                          <p className="text-sm text-gray-600">{option.description}</p>
                        </div>
                        <Button
                          size="sm"
                          style={{ backgroundColor: option.color }}
                          onClick={() => window.open(option.href, "_blank")}
                        >
                          {option.action}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* FAQ Section */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <div className="space-y-3">
              {faqs.map((faq, index) => (
                <Card key={index} className="border-0 shadow-sm">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg mt-1">
                        <HelpCircle size={16} className="text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-2">{faq.question}</h3>
                        <p className="text-sm text-gray-600">{faq.answer}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Emergency Contact */}
          <Card className="mt-6 border-2 border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="text-center">
                <h3 className="font-semibold text-red-900 mb-2">Need Urgent Help?</h3>
                <p className="text-sm text-red-800 mb-3">
                  For urgent issues or payment problems, contact us immediately
                </p>
                <Button
                  className="bg-red-600 hover:bg-red-700"
                  onClick={() =>
                    window.open("https://wa.me/2347018643642?text=URGENT:%20I%20need%20immediate%20help", "_blank")
                  }
                >
                  <MessageCircle size={16} className="mr-2" />
                  Emergency Support
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
