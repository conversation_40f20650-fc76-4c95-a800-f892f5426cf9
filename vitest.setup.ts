import "@testing-library/jest-dom"
import React from "react"

// Mock next/image to behave like a normal img in tests (no JSX in setup file)
vi.mock("next/image", () => ({
  __esModule: true,
  default: (props: any) => React.createElement("img", props),
}))

// Next.js alias support for tests
import path from "path"
import { fileURLToPath } from "url"

// No runtime setup needed beyond jest-dom; keep file for future hooks



