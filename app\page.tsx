"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { BookO<PERSON>, Shield, FileCheck, Star, TrendingUp, Plus, Database, Palette } from "lucide-react"
import { getBrowserSupabaseClient } from "@/lib/supabase-browser"
import { config } from "@/lib/config"
import Logo, { LogoIcon } from "@/components/logo"

// Check if Supabase environment variables are available
const supabase = getBrowserSupabaseClient()

interface Course {
  id: string
  title: string
  description: string
  price: number
  icon: string
  color: string
  whatsapp_group_link: string
  featured?: boolean
  active?: boolean
}

interface Coupon {
  id: string
  code: string
  discount_type: "percentage" | "fixed"
  discount_value: number
  expires_at: string
  usage_limit: number
  active: boolean
}

const defaultCourses: Course[] = [
  {
    id: "1",
    title: "Vibe Coding Master Class",
    description: "Master modern web development with React, Next.js, and TypeScript",
    price: 25000,
    icon: "code",
    color: "#3B82F6",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20Vibe%20Coding%20Master%20Class",
    featured: true,
  },
  {
    id: "2",
    title: "Ethical Hacking Master Class",
    description: "Learn cybersecurity fundamentals and ethical hacking techniques",
    price: 30000,
    icon: "shield",
    color: "#EF4444",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20Ethical%20Hacking%20Master%20Class",
  },
  {
    id: "3",
    title: "GRC Analyst Master Class",
    description: "Governance, Risk & Compliance analysis for enterprise security",
    price: 35000,
    icon: "check",
    color: "#10B981",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20GRC%20Analyst%20Master%20Class",
  },
  {
    id: "4",
    title: "Digital Marketing Bootcamp",
    description: "Complete digital marketing course covering SEO, SEM, Social Media",
    price: 20000,
    icon: "trending",
    color: "#F59E0B",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20Digital%20Marketing%20Bootcamp",
  },
  {
    id: "5",
    title: "Data Science Fundamentals",
    description: "Learn Python, SQL, Machine Learning, and Data Visualization",
    price: 40000,
    icon: "database",
    color: "#8B5CF6",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20Data%20Science%20Fundamentals",
  },
  {
    id: "6",
    title: "UI/UX Design Mastery",
    description: "Complete course on user interface and user experience design",
    price: 28000,
    icon: "palette",
    color: "#EC4899",
    whatsapp_group_link: "https://wa.me/2347018643642?text=I%20just%20paid%20for%20UI/UX%20Design%20Mastery",
  },
]

const getIcon = (iconName: string, color: string) => {
  const iconProps = { size: 24, color }
  switch (iconName) {
    case "code":
      return <BookOpen {...iconProps} />
    case "shield":
      return <Shield {...iconProps} />
    case "check":
      return <FileCheck {...iconProps} />
    case "trending":
      return <TrendingUp {...iconProps} />
    case "database":
      return <Database {...iconProps} />
    case "palette":
      return <Palette {...iconProps} />
    default:
      return <BookOpen {...iconProps} />
  }
}

export default function HomePage() {
  const [courses, setCourses] = useState<Course[]>(defaultCourses)
  const [showCouponInput, setShowCouponInput] = useState<string | null>(null)
  const [couponCode, setCouponCode] = useState("")
  const [appliedCoupons, setAppliedCoupons] = useState<Record<string, Coupon>>({})
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchCourses()
  }, [])

  const fetchCourses = async () => {
    if (!supabase) {
      console.log("Supabase not configured, using default courses")
      return
    }

    try {
      const { data, error } = await supabase
        .from("courses")
        .select("*")
        .eq("active", true)
        .order("created_at", { ascending: false })

      if (error) {
        // If table doesn't exist or other database error, use default courses
        console.log("Database error, using default courses:", error.message)
        return
      }

      if (data && data.length > 0) {
        setCourses(data)
      }
    } catch (error) {
      console.error("Error fetching courses:", error)
    }
  }

  const validateCoupon = async (code: string, courseId: string) => {
    if (!supabase) {
      toast({
        title: "Service Unavailable",
        description: "Coupon validation is not available in demo mode.",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      const { data, error } = await supabase
        .from("coupons")
        .select("*")
        .eq("code", code.toUpperCase())
        .eq("active", true)
        .single()

      if (error) {
        if (error.message.includes("does not exist") || error.message.includes("schema cache")) {
          toast({
            title: "Database Setup Required",
            description: "Please run the SQL scripts to set up the database first.",
            variant: "destructive",
          })
        } else if (error.code === "PGRST116") {
          // No rows returned
          toast({
            title: "Invalid Coupon",
            description: "The coupon code you entered is not valid.",
            variant: "destructive",
          })
        } else {
          toast({
            title: "Database Error",
            description: "Unable to validate coupon at this time.",
            variant: "destructive",
          })
        }
        return
      }

      const coupon = data as Coupon
      const now = new Date()
      const expiresAt = new Date(coupon.expires_at)

      if (expiresAt < now) {
        toast({
          title: "Expired Coupon",
          description: "This coupon has expired.",
          variant: "destructive",
        })
        return
      }

      setAppliedCoupons((prev) => ({ ...prev, [courseId]: coupon }))
      setShowCouponInput(null)
      setCouponCode("")

      toast({
        title: "Coupon Applied!",
        description: `${coupon.discount_type === "percentage" ? coupon.discount_value + "%" : "₦" + coupon.discount_value} discount applied.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to validate coupon. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const calculateDiscountedPrice = (originalPrice: number, coupon?: Coupon) => {
    if (!coupon) return originalPrice

    if (coupon.discount_type === "percentage") {
      return originalPrice - (originalPrice * coupon.discount_value) / 100
    } else {
      return Math.max(0, originalPrice - coupon.discount_value)
    }
  }

  const initializePaystack = () => {
    return new Promise((resolve) => {
      if ((window as any).PaystackPop) {
        resolve((window as any).PaystackPop)
        return
      }

      const script = document.createElement("script")
      script.src = "https://js.paystack.co/v1/inline.js"
      script.onload = () => resolve((window as any).PaystackPop)
      document.head.appendChild(script)
    })
  }

  const handlePayment = async (course: Course) => {
    const appliedCoupon = appliedCoupons[course.id]
    const originalPrice = course.price
    const finalPrice = calculateDiscountedPrice(originalPrice, appliedCoupon)
    const discountAmount = originalPrice - finalPrice

    try {
      const PaystackPop = await initializePaystack()

        const handler = PaystackPop.setup({
        key: config.payment.paystack.publicKey || "pk_test_ede666e704a4695e24ce26b7a6a032ed212397d7",
        email: "<EMAIL>",
        amount: finalPrice * 100,
        currency: "NGN",
        ref: `noisypay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        callback: (response: any) => {
          // Store purchase in Supabase
          if (supabase) {
            supabase
              .from("purchases")
              .insert({
                email: response.customer?.email || "<EMAIL>",
                course_id: course.id,
                coupon_code: appliedCoupon?.code || null,
                original_price: originalPrice,
                discount_amount: discountAmount,
                final_amount: finalPrice,
                status: "completed",
                paystack_reference: response.reference,
              })
              .then(({ error }) => {
                if (error) console.error("Error storing purchase:", error)
              })
          }

          toast({
            title: "Payment Successful!",
            description: `You've successfully purchased ${course.title}. Redirecting to WhatsApp group...`,
          })

          // Redirect to WhatsApp group after 2 seconds
          setTimeout(() => {
            window.open(course.whatsapp_group_link, "_blank")
          }, 2000)
        },
        onClose: () => {
          toast({
            title: "Payment Cancelled",
            description: "Your payment was cancelled.",
            variant: "destructive",
          })
        },
      })

      handler.openIframe()
    } catch (error) {
      console.error("Payment initialization error:", error)
      toast({
        title: "Payment Error",
        description: "Failed to initialize payment. Please try again.",
        variant: "destructive",
      })
    }
  }

  const featuredCourse = courses.find((c) => c.featured) || courses[0]
  const totalCourses = courses.length

  return (
    <div className="min-h-screen bg-[#F8FAFF] pb-20">
      {/* Hero Section */}
      <div className="bg-white px-4 pt-6 pb-8">
        <div className="max-w-md mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-xl font-bold text-gray-900">Hello Learner 👋</h1>
              <p className="text-sm text-gray-600">Ready to master new skills?</p>
            </div>
            <Logo size={40} />
          </div>

          {/* Main Course Card */}
          <Card className="bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 border-0 text-white mb-4 relative overflow-hidden">
            <CardContent className="p-6">
              {/* Decorative elements */}
              <div className="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full -translate-y-4 translate-x-4"></div>
              <div className="absolute bottom-0 right-0 w-32 h-32 bg-white/5 rounded-full translate-x-8 translate-y-8"></div>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <Button
                    size="sm"
                    className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold"
                    onClick={() => (window.location.href = "/courses")}
                  >
                    <Plus size={14} className="mr-1" />
                    Buy A Course
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="border-white/30 text-white hover:bg-white/10 bg-transparent"
                    onClick={() => (window.location.href = "/payments")}
                  >
                    View Paid Courses →
                  </Button>
                </div>

                <div>
                  <p className="text-white/80 text-sm mb-1">Available Courses</p>
                  <h2 className="text-3xl font-bold mb-2">{totalCourses} Courses</h2>
                  <div className="flex items-center gap-2">
                    <TrendingUp size={16} className="text-green-300" />
                    <span className="text-green-300 text-sm">Expert-led training programs</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <div className="flex gap-2 mb-6">
            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
            <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
          </div>
        </div>
      </div>

      {/* Featured Course Section */}
      <div className="px-4 mb-6">
        <div className="max-w-md mx-auto">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Featured Course</h3>

          {featuredCourse && (
            <Card className="bg-gradient-to-r from-gray-900 to-gray-800 text-white border-0 relative overflow-hidden">
              <CardContent className="p-4">
                <div className="absolute top-0 right-0 w-24 h-24 bg-yellow-400/20 rounded-full -translate-y-4 translate-x-4"></div>
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-3">
                    <Badge variant="secondary" className="bg-orange-500 text-white">
                      <Star size={12} className="mr-1" />
                      Featured
                    </Badge>
                  </div>
                  <h4 className="font-bold text-lg mb-2">{featuredCourse.title}</h4>
                  <p className="text-gray-300 text-sm mb-4">{featuredCourse.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-xl font-bold">₦{featuredCourse.price.toLocaleString()}</span>
                    <Button
                      size="sm"
                      className="bg-orange-500 hover:bg-orange-600"
                      onClick={() => handlePayment(featuredCourse)}
                    >
                      Buy Now
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Popular Courses Section */}
      <div className="px-4 mb-6">
        <div className="max-w-md mx-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Popular Courses</h3>
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600"
              onClick={() => (window.location.href = "/courses")}
            >
              View All →
            </Button>
          </div>

          <div className="grid grid-cols-2 gap-3 mb-4">
            {courses.slice(0, 4).map((course, index) => {
              const appliedCoupon = appliedCoupons[course.id]
              const discountedPrice = calculateDiscountedPrice(course.price, appliedCoupon)

              // Different solid colors for popular course cards
              const cardStyles = [
                { bg: "#F0FFF4", badge: "bg-green-500", hover: "#C6F6D5" }, // Green
                { bg: "#EBF8FF", badge: "bg-blue-500", hover: "#BEE3F8" }, // Blue
                { bg: "#FAF5FF", badge: "bg-purple-500", hover: "#E9D8FD" }, // Purple
                { bg: "#FFFAF0", badge: "bg-orange-500", hover: "#FED7AA" }, // Orange
              ]

              const cardStyle = cardStyles[index]

              return (
                <Card
                  key={course.id}
                  className="border-0 shadow-sm relative overflow-hidden transition-all duration-200 hover:shadow-md cursor-pointer"
                  style={{ backgroundColor: cardStyle.bg }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = cardStyle.hover
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = cardStyle.bg
                  }}
                >
                  <CardContent className="p-4">
                    <div className="absolute top-2 right-2">
                      <Badge className={`text-xs ${cardStyle.badge}`}>₦{Math.floor(discountedPrice / 1000)}K</Badge>
                    </div>

                    <div className="mt-8">
                      <h4 className="font-semibold text-gray-900 mb-1 text-sm">
                        {course.title.split(" ").slice(0, 2).join(" ")}
                      </h4>
                      <p className="text-xs text-gray-600 mb-3">{course.description.slice(0, 50)}...</p>
                      <Button
                        size="sm"
                        className={`w-full text-xs ${cardStyle.badge} hover:opacity-90 transition-all duration-200 hover:scale-105`}
                        onClick={() => handlePayment(course)}
                      >
                        Buy Now
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}
