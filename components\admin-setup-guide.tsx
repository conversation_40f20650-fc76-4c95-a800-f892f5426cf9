"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, CheckCircle, Shield, AlertTriangle } from "lucide-react"
import { useState, useEffect } from "react"

interface AdminStatus {
  isDefaultPassword: boolean
}

export default function AdminSetupGuide() {
  const [copiedStep, setCopiedStep] = useState<number | null>(null)
  const [newPassword, setNewPassword] = useState("")
  const [adminStatus, setAdminStatus] = useState<AdminStatus>({ isDefaultPassword: true })

  useEffect(() => {
    checkAdminStatus()
  }, [])

  const checkAdminStatus = async () => {
    try {
      const response = await fetch("/api/admin/status")
      if (response.ok) {
        const status = await response.json()
        setAdminStatus(status)
      }
    } catch (error) {
      console.error("Failed to check admin status:", error)
    }
  }

  const copyToClipboard = (text: string, step: number) => {
    navigator.clipboard.writeText(text)
    setCopiedStep(step)
    setTimeout(() => setCopiedStep(null), 2000)
  }

  const generateSecurePassword = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*"
    let password = ""
    for (let i = 0; i < 16; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    setNewPassword(password)
    return password
  }

  const deploymentGuides = [
    {
      platform: "Vercel",
      steps: [
        "Go to your Vercel dashboard",
        "Select your Noisypay project",
        "Go to Settings → Environment Variables",
        "Add or update: ADMIN_PASSWORD",
        "Set the value to your new secure password",
        "Redeploy your application",
      ],
      envExample: "ADMIN_PASSWORD=your_secure_password_here",
    },
    {
      platform: "Local Development",
      steps: [
        "Create or edit .env.local file in your project root",
        "Add the environment variable",
        "Restart your development server",
        "Test the new password",
      ],
      envExample: "ADMIN_PASSWORD=your_secure_password_here",
    },
    {
      platform: "Netlify",
      steps: [
        "Go to your Netlify dashboard",
        "Select your site",
        "Go to Site settings → Environment variables",
        "Add or update: ADMIN_PASSWORD",
        "Set the value to your new secure password",
        "Trigger a new deploy",
      ],
      envExample: "ADMIN_PASSWORD=your_secure_password_here",
    },
  ]

  return (
    <div className="min-h-screen bg-[#F8FAFF] p-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Password Setup</h1>
          <p className="text-gray-600">Configure a secure admin password for your Noisypay application</p>
        </div>

        {/* Current Status */}
        <Card
          className={`mb-6 border-2 ${adminStatus.isDefaultPassword ? "border-red-200 bg-red-50" : "border-green-200 bg-green-50"}`}
        >
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              {adminStatus.isDefaultPassword ? (
                <AlertTriangle className="text-red-600" size={24} />
              ) : (
                <Shield className="text-green-600" size={24} />
              )}
              <div className="flex-1">
                <h3 className={`font-semibold ${adminStatus.isDefaultPassword ? "text-red-900" : "text-green-900"}`}>
                  Current Admin Password Status
                </h3>
                <p className={`text-sm ${adminStatus.isDefaultPassword ? "text-red-800" : "text-green-800"}`}>
                  {adminStatus.isDefaultPassword
                    ? "⚠️ Using default password 'admin123' - Change immediately for security!"
                    : "✅ Custom admin password is configured"}
                </p>
              </div>
              <Badge
                className={adminStatus.isDefaultPassword ? "bg-red-100 text-red-700" : "bg-green-100 text-green-700"}
              >
                {adminStatus.isDefaultPassword ? "Insecure" : "Secure"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Password Generator */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield size={20} />
              Generate Secure Password
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex gap-2">
                <Button onClick={generateSecurePassword} className="bg-blue-600 hover:bg-blue-700">
                  Generate New Password
                </Button>
                {newPassword && (
                  <Button
                    variant="outline"
                    onClick={() => copyToClipboard(newPassword, 0)}
                    className="flex items-center gap-2"
                  >
                    {copiedStep === 0 ? <CheckCircle size={16} /> : <Copy size={16} />}
                    Copy Password
                  </Button>
                )}
              </div>
              {newPassword && (
                <div className="p-3 bg-gray-100 rounded-lg">
                  <p className="text-sm text-gray-600 mb-2">Generated Password:</p>
                  <code className="text-lg font-mono bg-white p-2 rounded border block">{newPassword}</code>
                  <p className="text-xs text-gray-500 mt-2">
                    ⚠️ Save this password securely - you'll need it to access the admin panel
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Platform-specific guides */}
        <div className="space-y-4">
          {deploymentGuides.map((guide, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="text-lg">{guide.platform} Setup</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="space-y-2">
                    {guide.steps.map((step, stepIndex) => (
                      <div key={stepIndex} className="flex items-start gap-2">
                        <Badge variant="outline" className="mt-0.5 text-xs">
                          {stepIndex + 1}
                        </Badge>
                        <span className="text-sm">{step}</span>
                      </div>
                    ))}
                  </div>

                  <div className="bg-gray-100 p-3 rounded-lg">
                    <p className="text-sm text-gray-600 mb-2">Environment Variable:</p>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 bg-white p-2 rounded border text-sm">{guide.envExample}</code>
                      <Button size="sm" variant="outline" onClick={() => copyToClipboard(guide.envExample, index + 1)}>
                        {copiedStep === index + 1 ? <CheckCircle size={16} /> : <Copy size={16} />}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Security Tips */}
        <Card className="mt-6 border-2 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-900">Security Best Practices</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-blue-800">
              <p>
                • <strong>Use a strong password:</strong> At least 12 characters with mixed case, numbers, and symbols
              </p>
              <p>
                • <strong>Don't share the password:</strong> Keep it secure and only share with trusted administrators
              </p>
              <p>
                • <strong>Regular updates:</strong> Change the password periodically for better security
              </p>
              <p>
                • <strong>Environment variables:</strong> Never commit passwords to your code repository
              </p>
              <p>
                • <strong>Test after changes:</strong> Always verify the new password works before closing this guide
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Quick Test */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Test Your New Password</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              After updating your environment variable and redeploying, test your new password:
            </p>
            <Button onClick={() => window.open("/admin", "_blank")} className="bg-green-600 hover:bg-green-700">
              Test Admin Login
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
