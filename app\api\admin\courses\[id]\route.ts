import { NextResponse } from "next/server"
import { createServiceClient } from "@/lib/supabase"
import { verifyAdminSession } from "@/lib/auth"

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    if (!verifyAdminSession()) return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    const body = await request.json()
    const supabase = createServiceClient()
    const { error } = await supabase.from("courses").update(body).eq("id", params.id)
    if (error) return NextResponse.json({ error: error.message }, { status: 500 })
    return NextResponse.json({ success: true })
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}

export async function DELETE(_request: Request, { params }: { params: { id: string } }) {
  try {
    if (!verifyAdminSession()) return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    const supabase = createServiceClient()
    const { error } = await supabase.from("courses").delete().eq("id", params.id)
    if (error) return NextResponse.json({ error: error.message }, { status: 500 })
    return NextResponse.json({ success: true })
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}


