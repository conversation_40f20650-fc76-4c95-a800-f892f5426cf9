"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, Clock, XCircle, MessageCircle } from "lucide-react"
import { getBrowserSupabaseClient } from "@/lib/supabase-browser"

// Check if Supabase environment variables are available
const supabase = getBrowserSupabaseClient()

interface Purchase {
  id: string
  email: string
  phone: string | null
  course_id: string
  coupon_code: string | null
  original_price: number
  discount_amount: number
  final_amount: number
  status: string
  paystack_reference: string
  whatsapp_joined: boolean
  created_at: string
  courses?: {
    title: string
    whatsapp_group_link: string
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "completed":
      return <CheckCircle size={20} className="text-green-600" />
    case "pending":
      return <Clock size={20} className="text-yellow-600" />
    case "failed":
      return <XCircle size={20} className="text-red-600" />
    default:
      return <Clock size={20} className="text-gray-600" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "completed":
      return "bg-green-100 text-green-700"
    case "pending":
      return "bg-yellow-100 text-yellow-700"
    case "failed":
      return "bg-red-100 text-red-700"
    default:
      return "bg-gray-100 text-gray-700"
  }
}

export default function PaymentsPage() {
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPurchases()
  }, [])

  const fetchPurchases = async () => {
    if (!supabase) {
      console.log("Supabase not configured, no purchases to show")
      setLoading(false)
      return
    }

    try {
      const { data, error } = await supabase
        .from("purchases")
        .select(
          `
          *,
          courses (
            title,
            whatsapp_group_link
          )
        `,
        )
        .order("created_at", { ascending: false })
        .limit(50)

      if (error) {
        // If table doesn't exist or other database error, show empty state
        console.log("Database error, no purchases to show:", error.message)
        setPurchases([])
        setLoading(false)
        return
      }

      setPurchases(data || [])
    } catch (error) {
      console.error("Error fetching purchases:", error)
      setPurchases([])
    } finally {
      setLoading(false)
    }
  }

  const joinWhatsAppGroup = (purchase: Purchase) => {
    if (purchase.courses?.whatsapp_group_link) {
      window.open(purchase.courses.whatsapp_group_link, "_blank")
    }
  }

  return (
    <div className="min-h-screen bg-[#F8FAFF] pb-20">
      <div className="bg-white px-4 py-6 mb-6">
        <div className="max-w-md mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment History</h1>
          <p className="text-gray-600">Track your course purchases and access WhatsApp groups</p>
        </div>
      </div>

      <div className="px-4">
        <div className="max-w-md mx-auto">
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-6 bg-gray-200 rounded w-16"></div>
                    </div>
                    <div className="h-3 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {purchases.map((purchase) => (
                <Card key={purchase.id} className="border-0 shadow-sm">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(purchase.status)}
                        <span className="font-semibold text-gray-900">
                          {purchase.courses?.title || `Course #${purchase.course_id.slice(0, 8)}`}
                        </span>
                      </div>
                      <Badge className={getStatusColor(purchase.status)}>{purchase.status}</Badge>
                    </div>

                    <div className="space-y-2 text-sm text-gray-600 mb-4">
                      <div className="flex justify-between">
                        <span>Amount Paid:</span>
                        <span className="font-semibold">₦{purchase.final_amount.toLocaleString()}</span>
                      </div>
                      {purchase.discount_amount > 0 && (
                        <div className="flex justify-between">
                          <span>Original Price:</span>
                          <span className="line-through">₦{purchase.original_price.toLocaleString()}</span>
                        </div>
                      )}
                      {purchase.discount_amount > 0 && (
                        <div className="flex justify-between">
                          <span>Discount:</span>
                          <span className="text-green-600">-₦{purchase.discount_amount.toLocaleString()}</span>
                        </div>
                      )}
                      {purchase.coupon_code && (
                        <div className="flex justify-between">
                          <span>Coupon Used:</span>
                          <span className="font-semibold">{purchase.coupon_code}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span>Date:</span>
                        <span>{new Date(purchase.created_at).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Reference:</span>
                        <span className="text-xs">{purchase.paystack_reference}</span>
                      </div>
                    </div>

                    {purchase.status === "completed" && purchase.courses?.whatsapp_group_link && (
                      <Button
                        className="w-full bg-green-600 hover:bg-green-700"
                        onClick={() => joinWhatsAppGroup(purchase)}
                      >
                        <MessageCircle size={16} className="mr-2" />
                        Join WhatsApp Group
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))}

              {purchases.length === 0 && !loading && (
                <div className="text-center py-12">
                  <CheckCircle size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No purchases yet</h3>
                  <p className="text-gray-600 mb-4">Your course purchases will appear here after payment</p>
                  <Button onClick={() => (window.location.href = "/")} className="bg-blue-600 hover:bg-blue-700">
                    Browse Courses
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
