import { logger } from "./logger"

export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean

  constructor(message: string, statusCode = 500, isOperational = true) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational

    Error.captureStackTrace(this, this.constructor)
  }
}

export const handleError = (error: Error | AppError, context?: Record<string, any>) => {
  logger.error(error.message, {
    stack: error.stack,
    context,
    isOperational: error instanceof AppError ? error.isOperational : false,
  })

  // In production, you might want to send to error reporting service
  if (process.env.NODE_ENV === "production") {
    // Example: Sentry.captureException(error)
  }
}

export const createErrorResponse = (error: Error | AppError) => {
  const isDevelopment = process.env.NODE_ENV === "development"

  if (error instanceof AppError) {
    return {
      error: {
        message: error.message,
        statusCode: error.statusCode,
        ...(isDevelopment && { stack: error.stack }),
      },
    }
  }

  return {
    error: {
      message: isDevelopment ? error.message : "Internal server error",
      statusCode: 500,
      ...(isDevelopment && { stack: error.stack }),
    },
  }
}
