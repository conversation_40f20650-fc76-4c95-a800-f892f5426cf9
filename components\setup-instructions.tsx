"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Database, Copy, CheckCircle } from "lucide-react"
import { useState } from "react"

export default function SetupInstructions() {
  const [copiedStep, setCopiedStep] = useState<number | null>(null)

  const copyToClipboard = (text: string, step: number) => {
    navigator.clipboard.writeText(text)
    setCopiedStep(step)
    setTimeout(() => setCopiedStep(null), 2000)
  }

  const steps = [
    {
      title: "1. Create Supabase Project",
      description: "Go to supabase.com and create a new project",
      action: "Open Supabase",
      link: "https://supabase.com/dashboard",
    },
    {
      title: "2. Run Database Scripts",
      description: "Copy and run the SQL scripts in your Supabase SQL editor",
      code: `-- First run: scripts/create-tables.sql
-- Then run: scripts/seed-data.sql`,
    },
    {
      title: "3. Get Environment Variables",
      description: "Copy your project URL and anon key from Supabase settings",
      code: `NEXT_PUBLIC_SUPABASE_URL=your_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key`,
    },
  ]

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database size={24} />
            Database Setup Instructions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {steps.map((step, index) => (
            <div key={index} className="border-l-4 border-blue-500 pl-4">
              <h3 className="font-semibold text-lg mb-2">{step.title}</h3>
              <p className="text-gray-600 mb-3">{step.description}</p>

              {step.link && (
                <Button onClick={() => window.open(step.link, "_blank")} className="bg-blue-600 hover:bg-blue-700">
                  {step.action}
                </Button>
              )}

              {step.code && (
                <div className="bg-gray-100 p-3 rounded-lg relative">
                  <pre className="text-sm overflow-x-auto">{step.code}</pre>
                  <Button
                    size="sm"
                    variant="outline"
                    className="absolute top-2 right-2 bg-transparent"
                    onClick={() => copyToClipboard(step.code!, index)}
                  >
                    {copiedStep === index ? <CheckCircle size={16} className="text-green-600" /> : <Copy size={16} />}
                  </Button>
                </div>
              )}
            </div>
          ))}

          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-900 mb-2">After Setup:</h4>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• All course data will be stored in your database</li>
              <li>• Admin panel will work for managing courses</li>
              <li>• Payment history will be tracked</li>
              <li>• Coupon system will be functional</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
