import { config, validateConfig } from "@/lib/config"

describe("config validation", () => {
  it("reports missing required envs", () => {
    const errors = validateConfig()
    // In test env we don't set envs; expect at least supabase and paystack keys missing
    expect(errors).toEqual(
      expect.arrayContaining([
        expect.stringMatching(/NEXT_PUBLIC_SUPABASE_URL/),
        expect.stringMatching(/NEXT_PUBLIC_SUPABASE_ANON_KEY/),
        expect.stringMatching(/NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY/),
      ])
    )
  })

  it("exposes feature flags", () => {
    expect(typeof config.features.analytics).toBe("boolean")
  })
})



