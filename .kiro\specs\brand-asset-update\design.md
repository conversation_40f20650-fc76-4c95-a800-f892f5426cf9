# Design Document

## Overview

This design outlines the systematic replacement of all NoisyPay branding assets throughout the PWA application. The new blue "N" logo and NoisyPay wordmark will replace existing placeholder assets across the favicon, PWA manifest icons, logo components, and all branding references.

## Architecture

### Asset Management Strategy
- **Centralized Asset Storage**: All new branding assets will be stored in the `/public` directory
- **Multiple Format Support**: Assets will be provided in appropriate sizes for different use cases
- **Consistent Naming Convention**: Clear, descriptive filenames that indicate purpose and size
- **Backward Compatibility**: Gradual replacement ensuring no broken references

### Asset Requirements
Based on the current implementation analysis, the following assets need to be created/replaced:

1. **Favicon Assets**
   - `favicon.ico` (16x16, 32x32, 48x48 multi-size ICO)
   - `favicon-16x16.png`
   - `favicon-32x32.png`

2. **PWA Manifest Icons**
   - `icon-192x192.png` (existing, needs replacement)
   - `icon-512x512.png` (existing, needs replacement)
   - `apple-touch-icon.png` (180x180 for iOS)

3. **General Logo Assets**
   - `logo.png` (existing, needs replacement - primary logo)
   - `logo-icon.png` (icon-only version for compact spaces)

## Components and Interfaces

### Logo Component Updates
The existing `Logo` component in `/components/logo.tsx` needs updates to:
- Reference the new logo assets
- Maintain existing props interface (`size`, `className`)
- Support both full logo and icon-only variants
- Ensure proper alt text reflects new branding

### Manifest Configuration
Two manifest files need synchronization:
- `/public/manifest.json` (static manifest)
- `/app/manifest.ts` (Next.js dynamic manifest)

Both should reference the same icon assets and maintain consistent metadata.

## Data Models

### Asset Reference Model
```typescript
interface BrandAsset {
  path: string;           // File path relative to /public
  size: string;          // Dimensions (e.g., "192x192")
  purpose: string;       // Usage context (favicon, pwa-icon, logo)
  format: string;        // File format (png, ico, svg)
}
```

### Manifest Icon Configuration
```typescript
interface ManifestIcon {
  src: string;           // Asset path
  sizes: string;         // Icon dimensions
  type: string;          // MIME type
  purpose?: string;      // "maskable" | "any" | "monochrome"
}
```

## Error Handling

### Asset Loading Fallbacks
- Implement graceful degradation if new assets fail to load
- Maintain existing asset references during transition
- Add proper error boundaries for image loading

### Validation Checks
- Verify all asset files exist before deployment
- Validate manifest icon references
- Check favicon format compatibility across browsers

## Testing Strategy

### Visual Regression Testing
- Compare before/after screenshots of key pages
- Test logo rendering across different screen sizes
- Verify PWA icon display on various mobile devices

### Functional Testing
- Validate favicon appears correctly in browser tabs
- Test PWA installation with new icons
- Verify logo component renders properly in all contexts

### Cross-Browser Compatibility
- Test favicon display in Chrome, Firefox, Safari, Edge
- Verify PWA manifest compatibility
- Check Apple Touch Icon functionality on iOS devices

### Asset Optimization Testing
- Verify file sizes are optimized for web delivery
- Test loading performance impact
- Validate image quality at different resolutions

## Implementation Approach

### Phase 1: Asset Preparation
1. Process the provided brand images into required formats and sizes
2. Generate favicon files in multiple formats
3. Create optimized PNG versions for PWA icons
4. Prepare both icon-only and full logo variants

### Phase 2: Asset Replacement
1. Replace existing placeholder assets in `/public` directory
2. Update favicon references in layout files
3. Modify PWA manifest configurations
4. Update logo component implementations

### Phase 3: Reference Updates
1. Update all hardcoded asset paths throughout the codebase
2. Modify metadata configurations (OpenGraph, Twitter cards)
3. Update any admin dashboard branding references
4. Clean up old/unused asset files

### Phase 4: Validation
1. Test all branding displays correctly across the application
2. Verify PWA installation uses new icons
3. Confirm favicon appears in browser tabs
4. Validate responsive logo behavior

## Technical Considerations

### Image Optimization
- Use Next.js Image component for automatic optimization
- Implement proper `priority` loading for above-the-fold logos
- Consider WebP format for modern browsers with PNG fallbacks

### Caching Strategy
- Update asset versioning to bust browser caches
- Consider CDN cache invalidation for immediate updates
- Implement proper cache headers for new assets

### Accessibility
- Maintain proper alt text for all logo images
- Ensure sufficient color contrast for the blue branding
- Verify logo remains visible in high contrast modes

### Performance Impact
- Monitor bundle size impact of new assets
- Optimize image compression without quality loss
- Implement lazy loading for non-critical logo instances