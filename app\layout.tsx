import type React from "react"
import type { Metada<PERSON>, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { Toaster } from "@/components/ui/toaster"
import BottomNavigation from "@/components/bottom-navigation"
import DemoBanner from "@/components/demo-banner"
import P<PERSON>InstallPrompt from "@/components/pwa-install-prompt"
import DatabaseSetupBanner from "@/components/database-setup-banner"
import MaintenanceMode from "@/components/maintenance-mode"
import { config } from "@/lib/config"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: {
    default: "NoisyPay - Online Training Payments",
    template: "%s | NoisyPay",
  },
  description:
    "Master new skills with expert-led training programs. Secure payments, instant access to WhatsApp groups.",
  keywords: ["online training", "courses", "payments", "skills", "education", "Nigeria"],
  authors: [{ name: "NoisyPay Team" }],
  creator: "NoisyPay",
  publisher: "NoisyPay",
  metadataBase: new URL(config.app.url),
  alternates: {
    canonical: "/",
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "NoisyPay",
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: "website",
    siteName: "NoisyPay",
    title: "NoisyPay - Online Training Payments",
    description: "Master new skills with expert-led training programs. Secure payments, instant access to WhatsApp groups.",
    url: config.app.url,
    images: [
      {
        url: "/logo.png",
        width: 512,
        height: 512,
        alt: "NoisyPay Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "NoisyPay - Online Training Payments",
    description: "Master new skills with expert-led training programs. Secure payments, instant access to WhatsApp groups.",
    images: ["/logo.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    // Add your verification codes here
    // google: "your-google-verification-code",
    // yandex: "your-yandex-verification-code",
  }
}

export const viewport: Viewport = {
  themeColor: "#3B82F6",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Check for maintenance mode
  if (config.features.maintenance) {
    return (
      <html lang="en">
        <head>
          <link rel="icon" href="/favicon.ico" sizes="any" />
          <link rel="icon" href="/favicon-16x16.png" sizes="16x16" type="image/png" />
          <link rel="icon" href="/favicon-32x32.png" sizes="32x32" type="image/png" />
          <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"
          />
        </head>
        <body className={inter.className}>
          <MaintenanceMode />
        </body>
      </html>
    )
  }

  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon-16x16.png" sizes="16x16" type="image/png" />
        <link rel="icon" href="/favicon-32x32.png" sizes="32x32" type="image/png" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"
        />
        {/* Preload critical resources */}
        <link rel="preload" href="/logo.png" as="image" />
        <link rel="dns-prefetch" href="//js.paystack.co" />
        <link rel="dns-prefetch" href="//api.paystack.co" />
        {config.database.url && <link rel="dns-prefetch" href={`//${new URL(config.database.url).hostname}`} />}
        {/* Performance hints for image optimization */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="color-scheme" content="light" />
      </head>
      <body className={inter.className}>
        <DemoBanner />
        <DatabaseSetupBanner />
        <PWAInstallPrompt />
        {children}
        <BottomNavigation />
        <Toaster />

        {/* Analytics and monitoring scripts would go here */}
        {config.features.analytics && process.env.NODE_ENV === "production" && (
          <>
            {/* Add your analytics scripts here */}
            {/* Example: Google Analytics, Vercel Analytics, etc. */}
          </>
        )}
      </body>
    </html>
  )
}
